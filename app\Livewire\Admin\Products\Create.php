<?php

namespace App\Livewire\Admin\Products;

use Livewire\Component;
use Livewire\Attributes\Validate;
use Livewire\Attributes\Layout;
use App\Models\Warehouse;

#[Layout('admin.layouts.master', ['titlePage' => 'Create Prodquct'])]
class Create extends Component
{
    #[Validate('required|string|max:225')]
    public $name_product;


    public $warehouses;
    public $quantities = []; // [warehouse_id => quantity]


    public function mount()
    {
        $this->warehouses = Warehouse::all();
    }

    public function save()
    {

    }

    public function render()
    {
        return view('livewire.admin.products.create');
    }
}
