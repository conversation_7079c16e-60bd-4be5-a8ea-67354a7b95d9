<div>
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <div class="my-auto">
            <h5 class="page-title fs-21 mb-1">{{ __('warehouses.all warehoueses') }}</h5>
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="javascript:void(0);">{{ __('products.dashboard') }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ __('side_bar.warehouses') }}</li>
                </ol>
            </nav>
        </div>

        <div class="d-flex my-xl-auto right-content align-items-center">
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-info btn-icon me-2 btn-b"><i
                        class="mdi mdi-filter-variant"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-danger btn-icon me-2"><i class="mdi mdi-star"></i></button>
            </div>
            <div class="pe-1 mb-xl-0">
                <button type="button" class="btn btn-warning  btn-icon me-2"><i class="mdi mdi-refresh"></i></button>
            </div>
        </div>
    </div>
    @if (session()->has('success'))
        <div class="alert alert-success">{{ session('success') }}</div>
    @endif
    <!-- Page Header Close -->
    <form method="POST" enctype="multipart/form-data">
        @csrf
        <div class="row">
                <!-- القسم الأيسر: الحقول النصية والقوائم المنسدلة -->
                <div class="col-xl-8">
                    <div class="card custom-card">
                        <div class="card-header justify-content-between">
                            <div class="card-title">
                                {{ __('products.البيانات الاساسية') }}
                            </div>
                        </div>
                        <div class="card-body">

                                <div class="row">
                                    <!-- اسم المنتج -->
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="productName" name="name_product"
                                                placeholder="{{ __('products.اسم المنتج') }}" required>
                                            <label for="productName">{{ __('products.اسم المنتج') }}</label>
                                        </div>
                                    </div>

                                    <!-- جدول كميات المخازن -->
                                    <div class="col-md-12 mb-3">
                                        <label class="mb-2 fw-bold">{{ __('products.كميات المخازن') }}</label>
                                        <div class="table-responsive">
                                            <table
                                                class="table table-striped table-hover align-middle border rounded shadow-sm">
                                                <thead class="table-primary">
                                                    <tr>
                                                        <th style="width:60%"><i class="mdi mdi-warehouse"></i>
                                                            {{ __('products.name_warehouses') }}</th>
                                                        <th style="width:40%"><i class="mdi mdi-numeric"></i>
                                                            {{ __('products.الكمية') }}</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td class="fw-semibold">{{ __('products.name_warehouses') }} 1</td>
                                                        <td>
                                                            <input type="number" class="form-control text-center"
                                                                value="0" min="0"
                                                                placeholder="{{ __('products.ادخل الكمية') }}">
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">{{ __('products.name_warehouses') }} 2</td>
                                                        <td>
                                                            <input type="number" class="form-control text-center"
                                                                value="0" min="0"
                                                                placeholder="{{ __('products.ادخل الكمية') }}">
                                                        </td>
                                                    </tr>
                                                    <tr>
                                                        <td class="fw-semibold">{{ __('products.name_warehouses') }} 3</td>
                                                        <td>
                                                            <input type="number" class="form-control text-center"
                                                                value="0" min="0"
                                                                placeholder="{{ __('products.ادخل الكمية') }}">
                                                        </td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>


                                    <!-- التصنيف (قائمة منسدلة) -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="category" name="category_id" required>
                                                <option value="">{{ __('products.choose_category') }}</option>
                                                {{-- @foreach ($categories as $category)
                                                    <option value="{{ $category->id }}">{{ $category->name }}</option>
                                                @endforeach --}}
                                            </select>
                                            <label for="category">{{ __('products.التصنيف') }}</label>
                                        </div>
                                    </div>

                                    <!-- البراند (قائمة منسدلة) -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="brand" name="brand_id" required>
                                                <option value="">{{ __('products.choose_brand') }}</option>
                                                {{-- @foreach ($brands as $brand)
                                                    <option value="{{ $brand->id }}">{{ $brand->name }}</option>
                                                @endforeach --}}
                                            </select>
                                            <label for="brand">{{ __('products.البراند') }}</label>
                                        </div>
                                    </div>



                                    <!-- سعر الشراء -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="number" step="0.01" class="form-control" id="purchasePrice"
                                                name="purchase_price" placeholder="{{ __('products.سعر الشراء') }}" required>
                                            <label for="purchasePrice">{{ __('products.سعر الشراء') }}</label>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="number" step="0.01" class="form-control" id="purchasePrice"
                                                name="purchase_price" placeholder="{{ __('products.سعر البيع') }}" required>
                                            <label for="purchasePrice">{{ __('products.سعر البيع') }}</label>
                                        </div>
                                    </div>

                                    <!-- وصف المنتج -->
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="description" name="description" placeholder="{{ __('products.وصف المنتج') }}"
                                                style="height: 100px"></textarea>
                                            <label for="description">{{ __('products.وصف المنتج') }}</label>
                                        </div>
                                    </div>

                                    <!-- كود المنتج -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="productCode" name="code"
                                                placeholder="{{ __('products.كود المنتج') }}" required>
                                            <label for="productCode">{{ __('products.كود المنتج') }}</label>
                                        </div>
                                    </div>

                                    <!-- الباركود -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="barcode" name="barcode"
                                                placeholder="{{ __('products.الباركود') }}">
                                            <label for="barcode">{{ __('products.الباركود') }}</label>
                                        </div>
                                    </div>

                                    <!-- الحد الأدنى للتنبيه -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="alertQuantity"
                                                name="alert_quantity" placeholder="{{ __('products.الحد الأدنى للتنبيه') }}"
                                                required>
                                            <label for="alertQuantity">{{ __('products.الحد الأدنى للتنبيه') }}</label>
                                        </div>
                                    </div>

                                    <!-- الوحدة -->
                                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="unit" name="unit" required>
                                                <option value="">{{ __('products.choose_unit') }}</option>
                                                <option value="piece">{{ __('products.piece') }}</option>
                                                <option value="kg">{{ __('products.kg') }}</option>
                                                <option value="liter">{{ __('products.liter') }}</option>
                                                <option value="box">{{ __('products.box') }}</option>
                                            </select>
                                            <label for="unit">{{ __('products.الوحدة') }}</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- زر الحفظ -->
                                <div class="col-md-12">
                                    <button type="submit"
                                        class="btn btn-primary">{{ __('buttons.submit_product') }}</button>
                                </div>

                        </div>
                    </div>
                </div>

                <!-- القسم الأيمن: تحميل الصور -->
                <div class="col-xl-4">
                    <div class="card custom-card">
                        <div class="card-header justify-content-between">
                            <div class="card-title">
                                {{ __('products.صور المنتج') }}
                            </div>
                        </div>
                        <div class="card-body">

                                <div class="row">
                                    <!-- حقل تحميل الصور -->
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="file" class="form-control" id="productImages" name="images[]"
                                                multiple>
                                            <label for="productImages">{{ __('products.صور المنتج') }}</label>
                                        </div>
                                    </div>

                                    <!-- معاينة الصور -->
                                    <div class="col-md-12">
                                        <div id="imagePreview" class="mt-3">
                                            <!-- سيتم عرض الصور المختارة هنا -->
                                        </div>
                                    </div>
                                </div>

                        </div>
                    </div>
                </div>
        </div>
    </form>
</div>
